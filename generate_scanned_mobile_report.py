#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成scanned和mobile类别专门对比报告
"""

import json
import base64
from pathlib import Path

def load_evaluation_results():
    """加载评测结果"""
    with open('evaluation_results.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def encode_image_to_base64(image_path):
    """将图片编码为base64"""
    try:
        if Path(image_path).exists():
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        else:
            # 尝试不同的扩展名
            base_path = Path(image_path).with_suffix('')
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                test_path = base_path.with_suffix(ext)
                if test_path.exists():
                    with open(test_path, 'rb') as f:
                        return base64.b64encode(f.read()).decode('utf-8')
    except Exception as e:
        print(f"无法加载图片 {image_path}: {e}")
    return None

def generate_report():
    """生成报告"""
    results = load_evaluation_results()

    # 筛选scanned和mobile数据
    target_categories = ['scanned', 'mobile']
    filtered_data = {}
    stats = {}

    # 处理新的数据结构
    for model_result in results.get('model_results', []):
        model_name = model_result['model_name']
        model_data = model_result.get('detailed_results', [])

        filtered_data[model_name] = []
        stats[model_name] = {}

        for category in target_categories:
            category_results = [r for r in model_data if r['category'] == category]
            filtered_data[model_name].extend(category_results)

            if category_results:
                accuracies = [r['accuracy'] for r in category_results]
                edit_distances = [r['edit_distance'] for r in category_results]

                stats[model_name][category] = {
                    'count': len(category_results),
                    'avg_accuracy': sum(accuracies) / len(accuracies),
                    'avg_edit_distance': sum(edit_distances) / len(edit_distances),
                    'max_accuracy': max(accuracies),
                    'min_accuracy': min(accuracies)
                }
    
    # 生成HTML
    html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scanned vs Mobile 类别对比报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: 'Segoe UI', sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }}
        .header {{ text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                  color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; }}
        .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .stat-card {{ background: white; padding: 25px; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }}
        .stat-title {{ font-size: 18px; font-weight: bold; color: #667eea; margin-bottom: 15px; }}
        .stat-value {{ font-size: 32px; font-weight: bold; color: #2d3748; margin-bottom: 5px; }}
        .chart-container {{ background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin-bottom: 30px; }}
        .chart-title {{ font-size: 20px; font-weight: bold; color: #2d3748; margin-bottom: 20px; text-align: center; }}
        .filter-buttons {{ text-align: center; margin-bottom: 20px; }}
        .filter-btn {{ background: #667eea; color: white; border: none; padding: 10px 20px; margin: 0 10px; 
                     border-radius: 25px; cursor: pointer; font-weight: bold; transition: all 0.3s ease; }}
        .filter-btn:hover {{ background: #5a67d8; transform: translateY(-2px); }}
        .filter-btn.active {{ background: #4c51bf; }}
        .samples-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }}
        .sample-card {{ background: white; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); overflow: hidden; }}
        .sample-header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; font-weight: bold; }}
        .sample-content {{ padding: 20px; }}
        .sample-image {{ width: 100%; max-height: 300px; object-fit: contain; border-radius: 10px; margin-bottom: 15px; }}
        .accuracy-badge {{ display: inline-block; padding: 5px 15px; border-radius: 20px; font-weight: bold; margin-bottom: 10px; }}
        .accuracy-high {{ background-color: #48bb78; color: white; }}
        .accuracy-medium {{ background-color: #ed8936; color: white; }}
        .accuracy-low {{ background-color: #f56565; color: white; }}
        .text-comparison {{ display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px; }}
        .text-box {{ background: #f7fafc; padding: 15px; border-radius: 10px; border-left: 4px solid #667eea; }}
        .text-label {{ font-weight: bold; color: #667eea; margin-bottom: 8px; }}
        .text-content {{ font-size: 14px; line-height: 1.5; max-height: 150px; overflow-y: auto; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Scanned vs Mobile 类别对比报告</h1>
        <p>专门分析扫描文档和手机拍摄两个类别的详细表现</p>
    </div>
    
    <div class="stats-grid">"""
    
    # 添加统计卡片
    for model_name in filtered_data.keys():
        for category in target_categories:
            if category in stats[model_name]:
                stat = stats[model_name][category]
                category_name = "扫描文档" if category == "scanned" else "手机拍摄"
                
                html += f"""
        <div class="stat-card">
            <div class="stat-title">{model_name} - {category_name}</div>
            <div class="stat-value">{stat['avg_accuracy']:.1%}</div>
            <div style="color: #718096;">平均准确率 (样本数: {stat['count']})</div>
            <div style="margin-top: 10px; font-size: 14px;">
                最高: {stat['max_accuracy']:.1%} | 最低: {stat['min_accuracy']:.1%}<br>
                平均编辑距离: {stat['avg_edit_distance']:.1f}
            </div>
        </div>"""
    
    # 准备图表数据
    model_names = list(filtered_data.keys())
    scanned_acc = []
    mobile_acc = []
    scanned_edit = []
    mobile_edit = []
    
    for model_name in model_names:
        scanned_acc.append(stats[model_name].get('scanned', {}).get('avg_accuracy', 0) * 100)
        mobile_acc.append(stats[model_name].get('mobile', {}).get('avg_accuracy', 0) * 100)
        scanned_edit.append(stats[model_name].get('scanned', {}).get('avg_edit_distance', 0))
        mobile_edit.append(stats[model_name].get('mobile', {}).get('avg_edit_distance', 0))
    
    html += f"""
    </div>
    
    <div class="chart-container">
        <div class="chart-title">准确率对比图</div>
        <canvas id="accuracyChart" width="400" height="200"></canvas>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">编辑距离对比图</div>
        <canvas id="editDistanceChart" width="400" height="200"></canvas>
    </div>
    
    <div class="filter-buttons">
        <button class="filter-btn active" onclick="filterSamples('all')">全部</button>
        <button class="filter-btn" onclick="filterSamples('scanned')">扫描文档</button>
        <button class="filter-btn" onclick="filterSamples('mobile')">手机拍摄</button>
    </div>
    
    <div class="samples-grid" id="samplesGrid">"""
    
    # 添加样本详情
    for model_name, model_results in filtered_data.items():
        for result in model_results:
            accuracy = result['accuracy']
            accuracy_class = 'accuracy-high' if accuracy >= 0.8 else 'accuracy-medium' if accuracy >= 0.6 else 'accuracy-low'
            
            # 尝试加载图片
            image_base64 = None
            if 'image_path' in result:
                image_base64 = encode_image_to_base64(result['image_path'])
            elif 'image' in result:
                image_base64 = encode_image_to_base64(result['image'])
            
            category_name = "扫描文档" if result['category'] == "scanned" else "手机拍摄"
            
            html += f"""
        <div class="sample-card" data-category="{result['category']}">
            <div class="sample-header">
                {result['filename']} - {model_name} ({category_name})
            </div>
            <div class="sample-content">"""
            
            if image_base64:
                html += f"""
                <img src="data:image/jpeg;base64,{image_base64}" alt="{result['filename']}" class="sample-image">"""
            
            html += f"""
                <div class="accuracy-badge {accuracy_class}">
                    准确率: {accuracy:.1%} | 编辑距离: {result['edit_distance']:.1f}
                </div>
                
                <div class="text-comparison">
                    <div class="text-box">
                        <div class="text-label">标准答案</div>
                        <div class="text-content">{result['gt_text'][:200]}{'...' if len(result['gt_text']) > 200 else ''}</div>
                    </div>
                    <div class="text-box">
                        <div class="text-label">识别结果</div>
                        <div class="text-content">{result['pred_text'][:200]}{'...' if len(result['pred_text']) > 200 else ''}</div>
                    </div>
                </div>
            </div>
        </div>"""
    
    # 完成HTML和JavaScript
    html += f"""
    </div>

    <script>
        // 图表数据
        const accuracyData = {{
            labels: {model_names},
            datasets: [{{
                label: '扫描文档',
                data: {scanned_acc},
                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                borderColor: 'rgba(102, 126, 234, 1)',
                borderWidth: 2
            }}, {{
                label: '手机拍摄',
                data: {mobile_acc},
                backgroundColor: 'rgba(118, 75, 162, 0.8)',
                borderColor: 'rgba(118, 75, 162, 1)',
                borderWidth: 2
            }}]
        }};
        
        const editDistanceData = {{
            labels: {model_names},
            datasets: [{{
                label: '扫描文档',
                data: {scanned_edit},
                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                borderColor: 'rgba(102, 126, 234, 1)',
                borderWidth: 2
            }}, {{
                label: '手机拍摄',
                data: {mobile_edit},
                backgroundColor: 'rgba(118, 75, 162, 0.8)',
                borderColor: 'rgba(118, 75, 162, 1)',
                borderWidth: 2
            }}]
        }};
        
        // 创建图表
        const accuracyCtx = document.getElementById('accuracyChart').getContext('2d');
        new Chart(accuracyCtx, {{
            type: 'bar',
            data: accuracyData,
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        beginAtZero: true,
                        max: 100,
                        title: {{ display: true, text: '准确率 (%)' }}
                    }}
                }},
                plugins: {{ legend: {{ display: true, position: 'top' }} }}
            }}
        }});
        
        const editDistanceCtx = document.getElementById('editDistanceChart').getContext('2d');
        new Chart(editDistanceCtx, {{
            type: 'bar',
            data: editDistanceData,
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        beginAtZero: true,
                        title: {{ display: true, text: '编辑距离' }}
                    }}
                }},
                plugins: {{ legend: {{ display: true, position: 'top' }} }}
            }}
        }});
        
        // 筛选功能
        function filterSamples(category) {{
            const cards = document.querySelectorAll('.sample-card');
            const buttons = document.querySelectorAll('.filter-btn');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            cards.forEach(card => {{
                if (category === 'all' || card.dataset.category === category) {{
                    card.style.display = 'block';
                }} else {{
                    card.style.display = 'none';
                }}
            }});
        }}
    </script>
</body>
</html>"""
    
    return html

if __name__ == "__main__":
    print("开始生成Scanned vs Mobile类别对比报告...")
    html_content = generate_report()
    
    with open('scanned_mobile_comparison.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("Scanned vs Mobile类别对比报告已生成: scanned_mobile_comparison.html")
