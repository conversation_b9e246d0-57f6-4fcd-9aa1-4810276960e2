#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成简洁的评测总结报告
"""

import json

def generate_summary_report():
    """生成简洁的评测总结"""
    
    # 读取评测结果
    with open('evaluation_results.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("="*80)
    print("OCR模型评测总结报告")
    print("="*80)
    print(f"评测时间: {data['evaluation_summary']['evaluation_timestamp']}")
    print(f"测试类别: {', '.join(data['evaluation_summary']['categories'])}")
    print(f"每类样本数: mobile(12), question(12), scanned(6)")
    print()
    
    # 提取关键数据
    models_data = {}
    for model_result in data['model_results']:
        model_name = model_result['model_name']
        stats = model_result['statistics']
        models_data[model_name] = stats
    
    # 总体对比
    print("【总体表现对比】")
    print("-" * 50)
    for model_name, stats in models_data.items():
        overall = stats['overall']
        print(f"{model_name}:")
        print(f"  总体准确率: {overall['avg_accuracy']*100:.2f}%")
        print(f"  平均编辑距离: {overall['avg_edit_distance']:.1f}")
        print(f"  测试样本数: {overall['sample_count']}")
        print()
    
    # 各类别详细对比
    categories = ['mobile', 'question', 'scanned']
    category_names = {'mobile': '手机拍摄', 'question': '题目图片', 'scanned': '扫描文档'}
    
    print("【各类别详细对比】")
    print("-" * 50)
    
    for category in categories:
        print(f"\n{category_names[category]} ({category}):")
        print("  模型名称    准确率    编辑距离   样本数")
        print("  " + "-" * 40)
        
        for model_name, stats in models_data.items():
            cat_stats = stats['by_category'][category]
            if cat_stats['sample_count'] > 0:
                print(f"  {model_name:<8} {cat_stats['avg_accuracy']*100:>6.2f}%  {cat_stats['avg_edit_distance']:>8.1f}  {cat_stats['sample_count']:>6}")
    
    # 关键发现
    print("\n【关键发现】")
    print("-" * 50)
    
    # 找出最佳表现
    base_overall = models_data['基础模型']['overall']['avg_accuracy']
    chain_overall = models_data['Chain模型']['overall']['avg_accuracy']
    
    improvement = (chain_overall - base_overall) * 100
    
    print(f"1. Chain模型相比基础模型总体准确率提升了 {improvement:.2f} 个百分点")
    
    # 各类别最佳表现
    for category in categories:
        base_acc = models_data['基础模型']['by_category'][category]['avg_accuracy']
        chain_acc = models_data['Chain模型']['by_category'][category]['avg_accuracy']
        
        if chain_acc > base_acc:
            improvement = (chain_acc - base_acc) * 100
            print(f"2. 在{category_names[category]}类别中，Chain模型比基础模型准确率高 {improvement:.2f} 个百分点")
        else:
            decline = (base_acc - chain_acc) * 100
            print(f"2. 在{category_names[category]}类别中，基础模型比Chain模型准确率高 {decline:.2f} 个百分点")
    
    # 最佳和最差类别
    print(f"\n3. 各模型表现最好的类别:")
    for model_name, stats in models_data.items():
        best_category = max(categories, key=lambda c: stats['by_category'][c]['avg_accuracy'])
        best_acc = stats['by_category'][best_category]['avg_accuracy'] * 100
        print(f"   {model_name}: {category_names[best_category]} ({best_acc:.2f}%)")
    
    print(f"\n4. 各模型表现最差的类别:")
    for model_name, stats in models_data.items():
        worst_category = min(categories, key=lambda c: stats['by_category'][c]['avg_accuracy'])
        worst_acc = stats['by_category'][worst_category]['avg_accuracy'] * 100
        print(f"   {model_name}: {category_names[worst_category]} ({worst_acc:.2f}%)")
    
    print("\n【评测说明】")
    print("-" * 50)
    print("• 准确率计算方式: (答案长度 - 编辑距离) / 答案长度")
    print("• 编辑距离: 预测文本与标准答案之间的字符级编辑距离")
    print("• 文本清理: 使用正则表达式清理了页码、标题等无关内容")
    print("• 评测基准: 以标准答案的字符数为准，忽略空格")
    
    print("\n" + "="*80)

if __name__ == "__main__":
    generate_summary_report()
