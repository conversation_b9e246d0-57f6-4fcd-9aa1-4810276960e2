#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成HTML格式的评测报告
"""

import json
from datetime import datetime

def generate_html_report(json_file_path: str, output_path: str = "evaluation_report.html"):
    """生成HTML评测报告"""

    # 读取JSON数据
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 生成CSS样式
    css_styles = """
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        .summary {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .model-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
        }
        .model-title {
            background-color: #3498db;
            color: white;
            padding: 10px 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
            font-size: 1.2em;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #bdc3c7;
            padding: 12px;
            text-align: center;
        }
        th {
            background-color: #34495e;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .accuracy-high {
            background-color: #d4edda;
            color: #155724;
            font-weight: bold;
        }
        .accuracy-medium {
            background-color: #fff3cd;
            color: #856404;
            font-weight: bold;
        }
        .accuracy-low {
            background-color: #f8d7da;
            color: #721c24;
            font-weight: bold;
        }
        .metric-card {
            display: inline-block;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            min-width: 150px;
            text-align: center;
        }
        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }
        .comparison-table {
            margin-top: 30px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #bdc3c7;
            color: #6c757d;
        }
    """
    
    # 创建HTML内容
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR模型评测报告</title>
    <style>
        {css_styles}
    </style>
</head>
<body>
    <div class="container">
        <h1>OCR模型评测报告</h1>

        <div class="summary">
            <h2>评测概要</h2>
            <p><strong>评测时间：</strong>{data['evaluation_summary']['evaluation_timestamp']}</p>
            <p><strong>评测模型数量：</strong>{data['evaluation_summary']['total_models']}</p>
            <p><strong>测试类别：</strong>{', '.join(data['evaluation_summary']['categories'])}</p>
            <p><strong>评测说明：</strong>本次评测使用文本清理后的内容进行比较，准确率以标准答案字数为基准计算。</p>
        </div>
"""

    # 生成模型部分
    model_sections_html = ""
    comparison_rows = []
    
    for model_result in data['model_results']:
        model_name = model_result['model_name']
        stats = model_result['statistics']
        
        # 总体指标卡片
        overall = stats['overall']
        metric_cards = f"""
        <div class="metric-card">
            <div class="metric-value">{overall['avg_accuracy']*100:.2f}%</div>
            <div class="metric-label">总体准确率</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">{overall['avg_edit_distance']:.1f}</div>
            <div class="metric-label">平均编辑距离</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">{overall['sample_count']}</div>
            <div class="metric-label">测试样本数</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">{overall['avg_gt_length']:.1f}</div>
            <div class="metric-label">平均答案长度</div>
        </div>
        """
        
        # 各类别详细表格
        category_rows = ""
        for category in ['mobile', 'question', 'scanned']:
            cat_stats = stats['by_category'][category]
            if cat_stats['sample_count'] > 0:
                accuracy = cat_stats['avg_accuracy'] * 100
                accuracy_class = 'accuracy-high' if accuracy >= 80 else 'accuracy-medium' if accuracy >= 60 else 'accuracy-low'
                
                category_rows += f"""
                <tr>
                    <td>{category}</td>
                    <td>{cat_stats['sample_count']}</td>
                    <td class="{accuracy_class}">{accuracy:.2f}%</td>
                    <td>{cat_stats['avg_edit_distance']:.2f}</td>
                    <td>{cat_stats['avg_gt_length']:.1f}</td>
                    <td>{cat_stats['avg_pred_length']:.1f}</td>
                </tr>
                """
                
                # 添加到对比表
                comparison_rows.append({
                    'model': model_name,
                    'category': category,
                    'sample_count': cat_stats['sample_count'],
                    'accuracy': accuracy,
                    'accuracy_class': accuracy_class,
                    'edit_distance': cat_stats['avg_edit_distance'],
                    'gt_length': cat_stats['avg_gt_length'],
                    'pred_length': cat_stats['avg_pred_length']
                })
        
        category_table = f"""
        <table>
            <thead>
                <tr>
                    <th>类别</th>
                    <th>样本数</th>
                    <th>准确率</th>
                    <th>编辑距离</th>
                    <th>答案长度</th>
                    <th>预测长度</th>
                </tr>
            </thead>
            <tbody>
                {category_rows}
            </tbody>
        </table>
        """
        
        model_section = f"""
        <div class="model-section">
            <div class="model-title">{model_name}</div>
            <h3>总体表现</h3>
            {metric_cards}
            <h3>各类别详细结果</h3>
            {category_table}
        </div>
        """

        model_sections_html += model_section
        
        # 添加总体结果到对比表
        overall_accuracy = overall['avg_accuracy'] * 100
        overall_accuracy_class = 'accuracy-high' if overall_accuracy >= 80 else 'accuracy-medium' if overall_accuracy >= 60 else 'accuracy-low'
        comparison_rows.append({
            'model': model_name,
            'category': '总体',
            'sample_count': overall['sample_count'],
            'accuracy': overall_accuracy,
            'accuracy_class': overall_accuracy_class,
            'edit_distance': overall['avg_edit_distance'],
            'gt_length': overall['avg_gt_length'],
            'pred_length': overall['avg_pred_length']
        })
    
    # 生成对比表
    comparison_table_rows = ""
    for row in comparison_rows:
        comparison_table_rows += f"""
        <tr>
            <td><strong>{row['model']}</strong></td>
            <td>{row['category']}</td>
            <td>{row['sample_count']}</td>
            <td class="{row['accuracy_class']}">{row['accuracy']:.2f}%</td>
            <td>{row['edit_distance']:.2f}</td>
            <td>{row['gt_length']:.1f}</td>
            <td>{row['pred_length']:.1f}</td>
        </tr>
        """
    
    comparison_table = f"""
    <table>
        <thead>
            <tr>
                <th>模型</th>
                <th>类别</th>
                <th>样本数</th>
                <th>准确率</th>
                <th>编辑距离</th>
                <th>答案长度</th>
                <th>预测长度</th>
            </tr>
        </thead>
        <tbody>
            {comparison_table_rows}
        </tbody>
    </table>
    """
    
    # 完成HTML内容
    html_content += f"""
        {model_sections_html}

        <div class="comparison-table">
            <h2>模型对比表</h2>
            {comparison_table}
        </div>

        <div class="footer">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>评测系统版本: v1.0</p>
        </div>
    </div>
</body>
</html>
"""
    
    # 保存HTML文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML报告已生成: {output_path}")

if __name__ == "__main__":
    generate_html_report("evaluation_results.json")
