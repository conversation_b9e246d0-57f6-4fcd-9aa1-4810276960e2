#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR结果评测系统
支持多模型、多类别的文本识别结果评测
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Tuple
import pandas as pd
from docx import Document
from text_cleaner import TextCleaner
from text_eval import TextEvaluator

class EvaluationSystem:
    """评测系统主类"""
    
    def __init__(self, test_dataset_path: str):
        """
        初始化评测系统
        
        Args:
            test_dataset_path: 测试数据集路径
        """
        self.test_dataset_path = Path(test_dataset_path)
        self.answers_path = self.test_dataset_path / "answers"
        self.images_path = self.test_dataset_path / "images"
        
        # 初始化工具
        self.cleaner = TextCleaner()
        self.evaluator = TextEvaluator(ignore_blank=True, ignore_case=False)
        
        # 类别定义
        self.categories = ["mobile", "question", "scanned"]
        
        # 加载标准答案
        self.ground_truth = self._load_ground_truth()
    
    def _read_docx_file(self, file_path: Path) -> str:
        """读取docx文件内容"""
        try:
            doc = Document(file_path)
            text_content = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())
            return '\n'.join(text_content)
        except Exception as e:
            print(f"读取docx文件失败 {file_path}: {e}")
            return ""

    def _load_ground_truth(self) -> Dict[str, str]:
        """加载所有标准答案"""
        ground_truth = {}

        for category in self.categories:
            category_path = self.answers_path / category
            if not category_path.exists():
                continue

            # 处理txt文件
            for answer_file in category_path.glob("*.txt"):
                # 从文件名提取图片标识
                filename = answer_file.stem.replace("-answer", "")
                
                # 读取答案内容
                with open(answer_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                ground_truth[filename] = content

            # 处理docx文件
            for answer_file in category_path.glob("*.docx"):
                # 从文件名提取图片标识
                filename = answer_file.stem

                # 读取docx答案内容
                content = self._read_docx_file(answer_file)
                if content:
                    ground_truth[filename] = content

        return ground_truth
    
    def _extract_filename_from_path(self, filepath: str) -> str:
        """从文件路径提取文件名（不含扩展名）"""
        return Path(filepath).stem

    def _infer_category_from_path(self, filepath: str) -> str:
        """从文件路径推断类别"""
        path_str = str(filepath).lower()
        if 'mobile' in path_str:
            return 'mobile'
        elif 'question' in path_str:
            return 'question'
        elif 'scanned' in path_str:
            return 'scanned'
        else:
            return 'unknown'
    
    def _clean_text_content(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        return self.cleaner.clean_text(text)
    
    def _get_text_from_result(self, result: Dict[str, Any], model_name: str = "") -> str:
        """从结果中提取文本内容"""
        # 根据模型类型选择正确的字段
        if model_name == "基础模型":
            # 基础模型使用 base_text (来自202508011919.json)
            if 'base_text' in result and result['base_text']:
                return result['base_text']
        elif model_name == "Chain模型":
            # Chain模型使用 text_content (来自test_result.json)
            if 'text_content' in result and result['text_content']:
                return result['text_content']

        # 通用处理逻辑
        # 优先使用 text_content
        if 'text_content' in result and result['text_content']:
            return result['text_content']

        # 其次使用 lines
        if 'lines' in result and result['lines']:
            if isinstance(result['lines'], list):
                return '\n'.join(str(line) for line in result['lines'] if line)
            else:
                return str(result['lines'])

        # 最后尝试其他字段
        if 'base_text' in result and result['base_text']:
            return result['base_text']

        if 'lora_text' in result and result['lora_text']:
            return result['lora_text']

        return ""
    
    def evaluate_single_result(self, result: Dict[str, Any], model_name: str = "") -> Dict[str, Any]:
        """评测单个结果"""
        # 提取文件名
        if 'filename' in result:
            filename = self._extract_filename_from_path(result['filename'])
        elif 'filepath' in result:
            filename = self._extract_filename_from_path(result['filepath'])
        elif 'image' in result:
            # 处理 chain 模型格式
            filename = self._extract_filename_from_path(result['image'])
        else:
            return None
        
        # 获取预测文本
        pred_text = self._get_text_from_result(result, model_name)
        pred_text_cleaned = self._clean_text_content(pred_text)

        # 获取标准答案
        if filename not in self.ground_truth:
            return None

        gt_text = self.ground_truth[filename]
        gt_text_cleaned = self._clean_text_content(gt_text)

        # 推断类别
        if 'category' in result:
            category = result['category']
        elif 'image' in result:
            category = self._infer_category_from_path(result['image'])
        elif 'filepath' in result:
            category = self._infer_category_from_path(result['filepath'])
        else:
            category = 'unknown'

        # 进行评测
        eval_result = self.evaluator.evaluate_text(gt_text_cleaned, pred_text_cleaned)

        # 添加额外信息
        eval_result.update({
            'filename': filename,
            'category': category,
            'original_pred_text': pred_text,
            'original_gt_text': gt_text,
            'cleaned_pred_text': pred_text_cleaned,
            'cleaned_gt_text': gt_text_cleaned
        })
        
        return eval_result
    
    def evaluate_model_results(self, results_data: Any, model_name: str) -> Dict[str, Any]:
        """评测单个模型的所有结果"""
        print(f"\n开始评测模型: {model_name}")
        
        # 处理不同的数据格式
        if isinstance(results_data, dict) and 'results' in results_data:
            results_list = results_data['results']
        elif isinstance(results_data, list):
            results_list = results_data
        else:
            raise ValueError(f"不支持的数据格式: {type(results_data)}")
        
        # 按类别分组评测结果
        category_results = {category: [] for category in self.categories}
        all_results = []
        
        for result in results_list:
            eval_result = self.evaluate_single_result(result, model_name)
            if eval_result is None:
                continue
            
            all_results.append(eval_result)
            category = eval_result['category']
            if category in category_results:
                category_results[category].append(eval_result)
        
        # 计算统计信息
        stats = self._calculate_statistics(all_results, category_results, model_name)
        
        return {
            'model_name': model_name,
            'all_results': all_results,
            'category_results': category_results,
            'statistics': stats
        }
    
    def _calculate_statistics(self, all_results: List[Dict], category_results: Dict, model_name: str) -> Dict[str, Any]:
        """计算统计信息"""
        stats = {
            'model_name': model_name,
            'total_samples': len(all_results),
            'overall': {},
            'by_category': {}
        }
        
        # 计算总体统计
        if all_results:
            accuracies = [r['accuracy'] for r in all_results]
            edit_distances = [r['edit_distance'] for r in all_results]
            gt_lengths = [r['gt_length'] for r in all_results]
            pred_lengths = [r['pred_length'] for r in all_results]
            
            stats['overall'] = {
                'avg_accuracy': sum(accuracies) / len(accuracies),
                'avg_edit_distance': sum(edit_distances) / len(edit_distances),
                'avg_gt_length': sum(gt_lengths) / len(gt_lengths),
                'avg_pred_length': sum(pred_lengths) / len(pred_lengths),
                'sample_count': len(all_results)
            }
        
        # 计算各类别统计
        for category, results in category_results.items():
            if results:
                accuracies = [r['accuracy'] for r in results]
                edit_distances = [r['edit_distance'] for r in results]
                gt_lengths = [r['gt_length'] for r in results]
                pred_lengths = [r['pred_length'] for r in results]
                
                stats['by_category'][category] = {
                    'avg_accuracy': sum(accuracies) / len(accuracies),
                    'avg_edit_distance': sum(edit_distances) / len(edit_distances),
                    'avg_gt_length': sum(gt_lengths) / len(gt_lengths),
                    'avg_pred_length': sum(pred_lengths) / len(pred_lengths),
                    'sample_count': len(results)
                }
            else:
                stats['by_category'][category] = {
                    'avg_accuracy': 0.0,
                    'avg_edit_distance': 0.0,
                    'avg_gt_length': 0.0,
                    'avg_pred_length': 0.0,
                    'sample_count': 0
                }
        
        return stats
    
    def print_evaluation_report(self, evaluation_results: List[Dict[str, Any]]):
        """打印评测报告"""
        print("\n" + "="*80)
        print("OCR 模型评测报告")
        print("="*80)
        
        # 打印各模型的详细结果
        for result in evaluation_results:
            model_name = result['model_name']
            stats = result['statistics']
            
            print(f"\n【{model_name}】")
            print("-" * 50)
            
            # 总体结果
            overall = stats['overall']
            if overall:
                print(f"总体结果 (样本数: {overall['sample_count']}):")
                print(f"  平均准确率: {overall['avg_accuracy']:.4f} ({overall['avg_accuracy']*100:.2f}%)")
                print(f"  平均编辑距离: {overall['avg_edit_distance']:.2f}")
                print(f"  平均答案长度: {overall['avg_gt_length']:.1f}")
                print(f"  平均预测长度: {overall['avg_pred_length']:.1f}")
            
            # 各类别结果
            print(f"\n各类别结果:")
            for category in self.categories:
                cat_stats = stats['by_category'][category]
                print(f"  {category} (样本数: {cat_stats['sample_count']}):")
                if cat_stats['sample_count'] > 0:
                    print(f"    准确率: {cat_stats['avg_accuracy']:.4f} ({cat_stats['avg_accuracy']*100:.2f}%)")
                    print(f"    编辑距离: {cat_stats['avg_edit_distance']:.2f}")
                    print(f"    答案长度: {cat_stats['avg_gt_length']:.1f}")
                    print(f"    预测长度: {cat_stats['avg_pred_length']:.1f}")
                else:
                    print(f"    无数据")
        
        # 打印对比表格
        self._print_comparison_table(evaluation_results)
    
    def _print_comparison_table(self, evaluation_results: List[Dict[str, Any]]):
        """打印对比表格"""
        print(f"\n{'='*80}")
        print("模型对比表")
        print(f"{'='*80}")
        
        # 准备表格数据
        table_data = []
        
        for result in evaluation_results:
            model_name = result['model_name']
            stats = result['statistics']
            
            # 总体行
            overall = stats['overall']
            if overall:
                table_data.append({
                    '模型': model_name,
                    '类别': '总体',
                    '样本数': overall['sample_count'],
                    '准确率': f"{overall['avg_accuracy']*100:.2f}%",
                    '编辑距离': f"{overall['avg_edit_distance']:.2f}",
                    '答案长度': f"{overall['avg_gt_length']:.1f}",
                    '预测长度': f"{overall['avg_pred_length']:.1f}"
                })
            
            # 各类别行
            for category in self.categories:
                cat_stats = stats['by_category'][category]
                if cat_stats['sample_count'] > 0:
                    table_data.append({
                        '模型': model_name,
                        '类别': category,
                        '样本数': cat_stats['sample_count'],
                        '准确率': f"{cat_stats['avg_accuracy']*100:.2f}%",
                        '编辑距离': f"{cat_stats['avg_edit_distance']:.2f}",
                        '答案长度': f"{cat_stats['avg_gt_length']:.1f}",
                        '预测长度': f"{cat_stats['avg_pred_length']:.1f}"
                    })
        
        # 创建并打印表格
        if table_data:
            df = pd.DataFrame(table_data)
            print(df.to_string(index=False))
    
    def save_detailed_results(self, evaluation_results: List[Dict[str, Any]], output_path: str):
        """保存详细结果到文件"""
        output_data = {
            'evaluation_summary': {
                'total_models': len(evaluation_results),
                'categories': self.categories,
                'evaluation_timestamp': str(pd.Timestamp.now())
            },
            'model_results': []
        }
        
        for result in evaluation_results:
            model_data = {
                'model_name': result['model_name'],
                'statistics': result['statistics'],
                'detailed_results': result['all_results']
            }
            output_data['model_results'].append(model_data)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细结果已保存到: {output_path}")

def main():
    """主函数"""
    # 配置路径
    test_dataset_path = "test_dataset"
    
    # 模型结果文件路径 (使用最新数据)
    model_files = {
        "基础模型": "202508011919.json",  # 使用 base_text
        "Chain模型": "test_result.json"   # 使用最新更新的 text_content (23:37)
        # 注意：微调模型不在最终展示中，所以这里不包含
    }
    
    # 初始化评测系统
    evaluator = EvaluationSystem(test_dataset_path)
    
    # 评测所有模型
    evaluation_results = []
    
    for model_name, file_path in model_files.items():
        if not os.path.exists(file_path):
            print(f"警告: 文件不存在 {file_path}")
            continue
        
        # 加载模型结果
        with open(file_path, 'r', encoding='utf-8') as f:
            model_data = json.load(f)
        
        # 评测模型
        result = evaluator.evaluate_model_results(model_data, model_name)
        evaluation_results.append(result)
    
    # 打印评测报告
    evaluator.print_evaluation_report(evaluation_results)
    
    # 保存详细结果
    evaluator.save_detailed_results(evaluation_results, "evaluation_results.json")

if __name__ == "__main__":
    main()
