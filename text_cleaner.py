#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
作文OCR结果文本清理工具
用于清理OCR识别结果中的无关内容
"""

import re
import json
import argparse
from typing import Dict, List, Any
from pathlib import Path

class TextCleaner:
    """文本清理器类"""
    
    def __init__(self):
        """初始化清理规则"""
        # 使用更强大的正则表达式模式
        self.pattern = r"""
^[ \t]*                             # 行首空白
(?:                                 # ↓——以下任何一种情况都算"无关内容"
   第?\s*\d+\s*页(?:\s*[\(（]共?\d+页?[\)）])?     # 🅰 页眉/页脚（第6页(共6页)…）
 | 语文\S*第?\d+\s*页                   # 🅱 "语文试题 第6页 共6页"
 | 核心诊断卷|语文试题|方格语文作业纸        # 🅲 各种卷名、纸张抬头
 | 请勿在此区域.*                       # 🅳 "请勿在此区域作答…"
 | 请在.*作答                          # 🅴 "请在各题目的答题区域内作答…"
 | 黑色矩形边框|答案无效                  # 🅵 答题卡边框提示
 | \d{1,2}:\d{2}\s*\d{1,2}月\d{1,2}日      # 🅶 13:12 4月26日周六
 | \d{1,2}:\d{2}                       # 🅷 裸时间戳
 | -?\d+\s*分(?:\/\d+\s*分)?             # 🅸 47分/60分、46分、-18
 | 章\)|TOOB|你好[！!]?|请稍等|△?SHI        # 🅹 零散口头/噪声
 | ```.*$                              # 🅺 代码围栏 ```、```hello
 | \[ERROR:.*$                         # 🅻 GPU OOM、日志行
 | [A-Za-z]{1,3}$                      # 🅼 "ABC""TOB" 之类 1-3 位纯英文
 | \d{4}$                              # 🅽 独立年份 "1974"
 | □                                   # 🅾 独立对勾框
 | 作文内容|作文题目|作文标题              # 作文相关标识
 | 田字格                              # 田字格
 | 考生须知|注意事项|答题卡               # 考试相关标识
 | 题目：?                             # 题目标识
 | 年\s*月\s*日\s*第?\s*页?               # "年 月 日 第 页"
 | 学生_+年_+月                        # "学生__________年____月"
 | .*请在.*答题区域.*作答.*              # "请在各题目的答题区域内作答"
 | .*超出.*答题区域.*无效.*              # "超出答题区域的答案无效"
 | .*答题区域.*无效.*                   # "答题区域的答案无效"
 | .*在.*区域.*作答.*                   # 各种答题区域提示
)[ \t]*$                               # 行尾空白
"""

        # 编译正则表达式以提高性能
        self.compiled_pattern = re.compile(self.pattern, re.IGNORECASE | re.MULTILINE | re.VERBOSE)

        # 保留原有的简单模式作为备用
        self.simple_patterns = [
            # 单独的标点符号或无意义字符
            r'^[。，、；：！？""''（）【】\s]*$',
        ]
        self.compiled_simple_patterns = [re.compile(pattern, re.IGNORECASE | re.MULTILINE) for pattern in self.simple_patterns]
    
    def clean_text(self, text: str) -> str:
        """清理单个文本

        Args:
            text: 待清理的文本

        Returns:
            清理后的文本
        """
        if not text or not isinstance(text, str):
            return text

        cleaned_text = text

        # 应用主要的清理规则（按行处理）
        lines = cleaned_text.split('\n')
        cleaned_lines = []

        for line in lines:
            # 使用强大的正则表达式清理每一行
            if not self.compiled_pattern.match(line):
                # 如果不匹配无关内容模式，则保留这一行
                # 但还要检查简单模式
                should_keep = True
                for simple_pattern in self.compiled_simple_patterns:
                    if simple_pattern.match(line):
                        should_keep = False
                        break

                if should_keep:
                    cleaned_lines.append(line.strip())

        # 重新组合文本
        cleaned_text = '\n'.join(cleaned_lines)

        # 清理多余的空白字符
        cleaned_text = re.sub(r'\n\s*\n', '\n', cleaned_text)  # 移除多余的空行
        cleaned_text = cleaned_text.strip()  # 移除整体首尾空白

        return cleaned_text
    
    def clean_lines(self, lines: List[str]) -> List[str]:
        """清理文本行列表
        
        Args:
            lines: 文本行列表
            
        Returns:
            清理后的文本行列表
        """
        if not lines or not isinstance(lines, list):
            return lines
        
        cleaned_lines = []
        for line in lines:
            if isinstance(line, str):
                cleaned_line = self.clean_text(line)
                if cleaned_line:  # 只保留非空的清理后文本
                    cleaned_lines.append(cleaned_line)
            else:
                cleaned_lines.append(line)
        
        return cleaned_lines
    
    def process_ocr_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个OCR结果
        
        Args:
            result: OCR结果字典
            
        Returns:
            清理后的OCR结果字典
        """
        if not isinstance(result, dict):
            return result
        
        # 创建结果副本
        cleaned_result = result.copy()
        
        # 清理text_content字段
        if 'text_content' in cleaned_result:
            cleaned_result['text_content'] = self.clean_text(cleaned_result['text_content'])
        
        # 清理lines字段
        if 'lines' in cleaned_result:
            cleaned_result['lines'] = self.clean_lines(cleaned_result['lines'])
        
        return cleaned_result
    
    def process_json_file(self, input_file: str, output_file: str = None) -> Dict[str, Any]:
        """处理JSON文件
        
        Args:
            input_file: 输入JSON文件路径
            output_file: 输出JSON文件路径（可选，默认为输入文件名_cleaned.json）
            
        Returns:
            处理后的数据
        """
        input_path = Path(input_file)
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 读取JSON文件
        with open(input_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 处理数据
        if isinstance(data, dict):
            # 处理results字段中的每个结果
            if 'results' in data and isinstance(data['results'], list):
                cleaned_results = []
                for result in data['results']:
                    cleaned_result = self.process_ocr_result(result)
                    cleaned_results.append(cleaned_result)
                
                # 更新数据
                cleaned_data = data.copy()
                cleaned_data['results'] = cleaned_results
                
                # 添加清理信息到summary
                if 'summary' in cleaned_data:
                    cleaned_data['summary']['text_cleaned'] = True
                    cleaned_data['summary']['cleaning_timestamp'] = str(Path().resolve())
            else:
                cleaned_data = data
        else:
            cleaned_data = data
        
        # 确定输出文件路径
        if output_file is None:
            output_file = input_path.parent / f"{input_path.stem}_cleaned{input_path.suffix}"
        
        # 保存清理后的数据
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(cleaned_data, f, ensure_ascii=False, indent=2)
        
        print(f"文本清理完成！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        
        # 统计信息
        if isinstance(cleaned_data, dict) and 'results' in cleaned_data:
            total_files = len(cleaned_data['results'])
            print(f"处理文件数量: {total_files}")
        
        return cleaned_data

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='OCR结果文本清理工具')
    parser.add_argument('input_file', help='输入JSON文件路径')
    parser.add_argument('-o', '--output', help='输出JSON文件路径（可选）')
    parser.add_argument('--preview', action='store_true', help='预览清理效果（不保存文件）')
    
    args = parser.parse_args()
    
    try:
        cleaner = TextCleaner()
        
        if args.preview:
            # 预览模式：只显示清理效果
            with open(args.input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, dict) and 'results' in data:
                print("=== 文本清理预览 ===")
                for i, result in enumerate(data['results'][:3]):  # 只显示前3个结果
                    if 'text_content' in result:
                        original = result['text_content']
                        cleaned = cleaner.clean_text(original)
                        print(f"\n文件 {i+1}: {result.get('filename', 'Unknown')}")
                        print(f"原始文本长度: {len(original)}")
                        print(f"清理后长度: {len(cleaned)}")
                        print(f"原始文本前100字符: {original[:100]}...")
                        print(f"清理后前100字符: {cleaned[:100]}...")
                        print("-" * 50)
        else:
            # 正常处理模式
            result = cleaner.process_json_file(args.input_file, args.output)
            
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())