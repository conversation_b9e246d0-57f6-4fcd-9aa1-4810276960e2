#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成scanned和mobile类别对比报告
专门分析这两个类别的详细表现
"""

import json
import pandas as pd
from pathlib import Path
import base64

def load_evaluation_results():
    """加载评测结果"""
    with open('evaluation_results.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def encode_image_to_base64(image_path):
    """将图片编码为base64"""
    try:
        if Path(image_path).exists():
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        else:
            # 尝试不同的扩展名
            base_path = Path(image_path).with_suffix('')
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                test_path = base_path.with_suffix(ext)
                if test_path.exists():
                    with open(test_path, 'rb') as f:
                        return base64.b64encode(f.read()).decode('utf-8')
    except Exception as e:
        print(f"无法加载图片 {image_path}: {e}")
    return None

def generate_category_comparison_report():
    """生成scanned和mobile类别对比报告"""
    
    # 加载数据
    results = load_evaluation_results()
    
    # 筛选scanned和mobile类别的数据
    target_categories = ['scanned', 'mobile']
    filtered_results = {}
    
    for model_name, model_data in results.items():
        if model_name == 'summary':
            continue
            
        filtered_results[model_name] = []
        for result in model_data:
            if result['category'] in target_categories:
                filtered_results[model_name].append(result)
    
    # 生成HTML报告
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scanned vs Mobile 类别对比报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
            color: #333;
        }}
        
        .header {{
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .stat-card {{
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }}
        
        .stat-title {{
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 15px;
        }}
        
        .stat-value {{
            font-size: 32px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }}
        
        .stat-label {{
            color: #718096;
            font-size: 14px;
        }}
        
        .chart-container {{
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }}
        
        .chart-title {{
            font-size: 20px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
        }}
        
        .samples-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }}
        
        .sample-card {{
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .sample-header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            font-weight: bold;
        }}
        
        .sample-content {{
            padding: 20px;
        }}
        
        .sample-image {{
            width: 100%;
            max-height: 300px;
            object-fit: contain;
            border-radius: 10px;
            margin-bottom: 15px;
        }}
        
        .accuracy-badge {{
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }}
        
        .accuracy-high {{ background-color: #48bb78; color: white; }}
        .accuracy-medium {{ background-color: #ed8936; color: white; }}
        .accuracy-low {{ background-color: #f56565; color: white; }}
        
        .text-comparison {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }}
        
        .text-box {{
            background: #f7fafc;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }}
        
        .text-label {{
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }}
        
        .text-content {{
            font-size: 14px;
            line-height: 1.5;
            max-height: 150px;
            overflow-y: auto;
        }}
        
        .filter-buttons {{
            text-align: center;
            margin-bottom: 20px;
        }}
        
        .filter-btn {{
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }}
        
        .filter-btn:hover {{
            background: #5a67d8;
            transform: translateY(-2px);
        }}
        
        .filter-btn.active {{
            background: #4c51bf;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Scanned vs Mobile 类别对比报告</h1>
        <p>专门分析扫描文档和手机拍摄两个类别的详细表现</p>
    </div>
"""
    
    # 计算统计数据
    stats = {}
    for model_name, model_results in filtered_results.items():
        stats[model_name] = {}
        for category in target_categories:
            category_results = [r for r in model_results if r['category'] == category]
            if category_results:
                accuracies = [r['accuracy'] for r in category_results]
                edit_distances = [r['edit_distance'] for r in category_results]
                
                stats[model_name][category] = {
                    'count': len(category_results),
                    'avg_accuracy': sum(accuracies) / len(accuracies),
                    'avg_edit_distance': sum(edit_distances) / len(edit_distances),
                    'max_accuracy': max(accuracies),
                    'min_accuracy': min(accuracies)
                }
    
    # 添加统计卡片
    html_content += """
    <div class="stats-grid">
"""
    
    for model_name in filtered_results.keys():
        for category in target_categories:
            if category in stats[model_name]:
                stat = stats[model_name][category]
                category_name = "扫描文档" if category == "scanned" else "手机拍摄"
                
                html_content += f"""
        <div class="stat-card">
            <div class="stat-title">{model_name} - {category_name}</div>
            <div class="stat-value">{stat['avg_accuracy']:.1%}</div>
            <div class="stat-label">平均准确率</div>
            <div style="margin-top: 10px;">
                <small>样本数: {stat['count']} | 最高: {stat['max_accuracy']:.1%} | 最低: {stat['min_accuracy']:.1%}</small>
            </div>
        </div>
"""
    
    html_content += """
    </div>
    
    <!-- 图表容器 -->
    <div class="chart-container">
        <div class="chart-title">准确率对比图</div>
        <canvas id="accuracyChart" width="400" height="200"></canvas>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">编辑距离对比图</div>
        <canvas id="editDistanceChart" width="400" height="200"></canvas>
    </div>
    
    <!-- 筛选按钮 -->
    <div class="filter-buttons">
        <button class="filter-btn active" onclick="filterSamples('all')">全部</button>
        <button class="filter-btn" onclick="filterSamples('scanned')">扫描文档</button>
        <button class="filter-btn" onclick="filterSamples('mobile')">手机拍摄</button>
    </div>
    
    <!-- 样本详情 -->
    <div class="samples-grid" id="samplesGrid">
"""
    
    return html_content, stats, filtered_results

if __name__ == "__main__":
    print("开始生成Scanned vs Mobile类别对比报告...")
    html_content, stats, filtered_results = generate_category_comparison_report()
    
    # 这里需要继续添加样本详情和JavaScript代码
    # 由于文件长度限制，我会在下一步继续完成
    
    print("类别对比报告生成中...")
