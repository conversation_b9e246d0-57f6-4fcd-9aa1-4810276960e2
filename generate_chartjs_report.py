#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成带Chart.js可视化的HTML评测报告
"""

import json
import os
from pathlib import Path
from datetime import datetime
import base64

def encode_image_to_base64(image_path):
    """将图片编码为base64字符串"""
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
            base64_string = base64.b64encode(image_data).decode('utf-8')
            # 根据文件扩展名确定MIME类型
            ext = Path(image_path).suffix.lower()
            if ext in ['.jpg', '.jpeg']:
                mime_type = 'image/jpeg'
            elif ext == '.png':
                mime_type = 'image/png'
            else:
                mime_type = 'image/jpeg'  # 默认
            return f"data:{mime_type};base64,{base64_string}"
    except Exception as e:
        print(f"无法加载图片 {image_path}: {e}")
        return None

def generate_chartjs_report():
    """生成带Chart.js的HTML报告"""
    
    print("开始生成Chart.js可视化报告...")
    
    # 读取JSON数据
    with open("evaluation_results.json", 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    test_dataset = Path("test_dataset")
    images_path = test_dataset / "images"
    
    # 提取图表数据
    chart_data = {
        'models': [],
        'categories': ['mobile', 'question', 'scanned'],
        'category_names': {'mobile': '手机拍摄', 'question': '题目图片', 'scanned': '扫描文档'},
        'accuracy_data': {},
        'edit_distance_data': {}
    }
    
    for model_result in data['model_results']:
        model_name = model_result['model_name']
        chart_data['models'].append(model_name)
        chart_data['accuracy_data'][model_name] = []
        chart_data['edit_distance_data'][model_name] = []
        
        stats = model_result['statistics']
        for category in chart_data['categories']:
            cat_stats = stats['by_category'][category]
            chart_data['accuracy_data'][model_name].append(cat_stats['avg_accuracy'] * 100)
            chart_data['edit_distance_data'][model_name].append(cat_stats['avg_edit_distance'])
    
    # HTML内容
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR模型评测报告 - 可视化版</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
        }}
        .summary {{
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        .charts-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }}
        .chart-container {{
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .chart-title {{
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }}
        .full-width-chart {{
            grid-column: 1 / -1;
        }}
        .stats-summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }}
        .stat-card {{
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }}
        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }}
        .stat-label {{
            color: #6c757d;
            font-size: 0.9em;
        }}
        .model-comparison {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }}
        .model-card {{
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            background-color: #fff;
        }}
        .model-title {{
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
            padding: 10px;
            border-radius: 5px;
        }}
        .base-model .model-title {{
            background-color: #fff3cd;
            color: #856404;
        }}
        .chain-model .model-title {{
            background-color: #d1ecf1;
            color: #0c5460;
        }}
        .performance-metrics {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }}
        .metric-item {{
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }}
        .metric-value {{
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }}
        .metric-name {{
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }}
        .category-breakdown {{
            margin-top: 20px;
        }}
        .category-item {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background-color: #f8f9fa;
            border-radius: 5px;
        }}
        .category-name {{
            font-weight: bold;
        }}
        .category-accuracy {{
            padding: 5px 10px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
        }}
        .accuracy-high {{ background-color: #28a745; }}
        .accuracy-medium {{ background-color: #ffc107; color: #212529; }}
        .accuracy-low {{ background-color: #dc3545; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>OCR模型评测报告 - 可视化版</h1>
        
        <div class="summary">
            <h2>评测概要</h2>
            <p><strong>评测时间：</strong>{data['evaluation_summary']['evaluation_timestamp']}</p>
            <p><strong>评测模型数量：</strong>{data['evaluation_summary']['total_models']}</p>
            <p><strong>测试类别：</strong>{', '.join(data['evaluation_summary']['categories'])}</p>
            <p><strong>评测说明：</strong>本次评测使用文本清理后的内容进行比较，准确率以标准答案字数为基准计算。</p>
        </div>
        
        <div class="stats-summary">
    """
    
    # 添加统计卡片
    for model_result in data['model_results']:
        model_name = model_result['model_name']
        overall = model_result['statistics']['overall']
        
        html_content += f"""
            <div class="stat-card">
                <div class="stat-value">{overall['avg_accuracy']*100:.1f}%</div>
                <div class="stat-label">{model_name}<br>总体准确率</div>
            </div>
        """
    
    html_content += f"""
        </div>
        
        <div class="charts-grid">
            <div class="chart-container">
                <div class="chart-title">各类别准确率对比</div>
                <canvas id="accuracyChart"></canvas>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">各类别编辑距离对比</div>
                <canvas id="editDistanceChart"></canvas>
            </div>
            
            <div class="chart-container full-width-chart">
                <div class="chart-title">模型整体表现雷达图</div>
                <canvas id="radarChart"></canvas>
            </div>
        </div>
        
        <div class="model-comparison">
    """
    
    # 添加模型对比卡片
    for model_result in data['model_results']:
        model_name = model_result['model_name']
        stats = model_result['statistics']
        overall = stats['overall']
        
        model_class = 'base-model' if '基础' in model_name else 'chain-model'
        
        html_content += f"""
            <div class="model-card {model_class}">
                <div class="model-title">{model_name}</div>
                
                <div class="performance-metrics">
                    <div class="metric-item">
                        <div class="metric-value">{overall['avg_accuracy']*100:.1f}%</div>
                        <div class="metric-name">总体准确率</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">{overall['avg_edit_distance']:.1f}</div>
                        <div class="metric-name">平均编辑距离</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">{overall['sample_count']}</div>
                        <div class="metric-name">测试样本数</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">{overall['avg_gt_length']:.0f}</div>
                        <div class="metric-name">平均答案长度</div>
                    </div>
                </div>
                
                <div class="category-breakdown">
                    <h4>各类别表现</h4>
        """
        
        categories = ['mobile', 'question', 'scanned']
        category_names = {'mobile': '手机拍摄', 'question': '题目图片', 'scanned': '扫描文档'}
        
        for category in categories:
            cat_stats = stats['by_category'][category]
            if cat_stats['sample_count'] > 0:
                accuracy = cat_stats['avg_accuracy'] * 100
                accuracy_class = 'accuracy-high' if accuracy >= 80 else 'accuracy-medium' if accuracy >= 60 else 'accuracy-low'
                
                html_content += f"""
                    <div class="category-item">
                        <span class="category-name">{category_names[category]}</span>
                        <span class="category-accuracy {accuracy_class}">{accuracy:.1f}%</span>
                    </div>
                """
        
        html_content += """
                </div>
            </div>
        """
    
    html_content += """
        </div>
        
        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #6c757d;">
            <p>报告生成时间: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
            <p>评测系统版本: v3.0 Chart.js Enhanced</p>
        </div>
    </div>
    
    <script>
        // 图表数据
        const chartData = """ + json.dumps(chart_data, ensure_ascii=False) + """;
        
        // 准确率对比图
        const accuracyCtx = document.getElementById('accuracyChart').getContext('2d');
        new Chart(accuracyCtx, {
            type: 'bar',
            data: {
                labels: Object.values(chartData.category_names),
                datasets: chartData.models.map((model, index) => ({
                    label: model,
                    data: chartData.accuracy_data[model],
                    backgroundColor: index === 0 ? 'rgba(52, 152, 219, 0.8)' : 'rgba(231, 76, 60, 0.8)',
                    borderColor: index === 0 ? 'rgba(52, 152, 219, 1)' : 'rgba(231, 76, 60, 1)',
                    borderWidth: 1
                }))
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.y.toFixed(1) + '%';
                            }
                        }
                    }
                }
            }
        });
        
        // 编辑距离对比图
        const editDistanceCtx = document.getElementById('editDistanceChart').getContext('2d');
        new Chart(editDistanceCtx, {
            type: 'line',
            data: {
                labels: Object.values(chartData.category_names),
                datasets: chartData.models.map((model, index) => ({
                    label: model,
                    data: chartData.edit_distance_data[model],
                    borderColor: index === 0 ? 'rgba(52, 152, 219, 1)' : 'rgba(231, 76, 60, 1)',
                    backgroundColor: index === 0 ? 'rgba(52, 152, 219, 0.1)' : 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    fill: true
                }))
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // 雷达图
        const radarCtx = document.getElementById('radarChart').getContext('2d');
        new Chart(radarCtx, {
            type: 'radar',
            data: {
                labels: Object.values(chartData.category_names),
                datasets: chartData.models.map((model, index) => ({
                    label: model,
                    data: chartData.accuracy_data[model],
                    borderColor: index === 0 ? 'rgba(52, 152, 219, 1)' : 'rgba(231, 76, 60, 1)',
                    backgroundColor: index === 0 ? 'rgba(52, 152, 219, 0.2)' : 'rgba(231, 76, 60, 0.2)',
                    pointBackgroundColor: index === 0 ? 'rgba(52, 152, 219, 1)' : 'rgba(231, 76, 60, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: index === 0 ? 'rgba(52, 152, 219, 1)' : 'rgba(231, 76, 60, 1)'
                }))
            },
            options: {
                responsive: true,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
    """
    
    # 保存HTML文件
    with open("chartjs_evaluation_report.html", 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("Chart.js可视化报告已生成: chartjs_evaluation_report.html")

if __name__ == "__main__":
    generate_chartjs_report()
