#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成增强版HTML评测报告，包含图片展示和可视化图表
"""

import json
import os
from pathlib import Path
from datetime import datetime
import base64

def encode_image_to_base64(image_path):
    """将图片编码为base64字符串"""
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
            base64_string = base64.b64encode(image_data).decode('utf-8')
            # 根据文件扩展名确定MIME类型
            ext = Path(image_path).suffix.lower()
            if ext in ['.jpg', '.jpeg']:
                mime_type = 'image/jpeg'
            elif ext == '.png':
                mime_type = 'image/png'
            else:
                mime_type = 'image/jpeg'  # 默认
            return f"data:{mime_type};base64,{base64_string}"
    except Exception as e:
        print(f"无法加载图片 {image_path}: {e}")
        return None

def generate_enhanced_html_report(json_file_path: str, test_dataset_path: str, output_path: str = "enhanced_evaluation_report.html"):
    """生成增强版HTML评测报告"""
    
    # 读取JSON数据
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    test_dataset = Path(test_dataset_path)
    images_path = test_dataset / "images"
    
    # CSS样式
    css_styles = """
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 30px;
        }
        .summary {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .charts-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }
        .chart-box {
            flex: 1;
            min-width: 300px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
        }
        .sample-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(800px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .sample-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .sample-header {
            background-color: #34495e;
            color: white;
            padding: 10px 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 10px 10px 0 0;
            font-weight: bold;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .sample-image {
            max-width: 100%;
            max-height: 400px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .text-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        .text-box {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .text-box h4 {
            margin: 0 0 10px 0;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 14px;
        }
        .ground-truth h4 {
            background-color: #d4edda;
            color: #155724;
        }
        .base-model h4 {
            background-color: #fff3cd;
            color: #856404;
        }
        .chain-model h4 {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .text-content {
            font-size: 13px;
            line-height: 1.5;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .metrics {
            display: flex;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        .metric {
            background-color: #e9ecef;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: bold;
        }
        .accuracy-high { background-color: #d4edda; color: #155724; }
        .accuracy-medium { background-color: #fff3cd; color: #856404; }
        .accuracy-low { background-color: #f8d7da; color: #721c24; }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        .stats-table th {
            background-color: #34495e;
            color: white;
            font-weight: bold;
        }
        .stats-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .category-filter {
            margin: 20px 0;
            text-align: center;
        }
        .filter-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .filter-btn:hover {
            background-color: #2980b9;
        }
        .filter-btn.active {
            background-color: #e74c3c;
        }
        
        .chart-placeholder {
            width: 100%;
            height: 300px;
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 16px;
            border-radius: 8px;
        }
    """
    
    # JavaScript代码
    js_code = """
        function filterSamples(category) {
            const samples = document.querySelectorAll('.sample-card');
            const buttons = document.querySelectorAll('.filter-btn');
            
            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 显示/隐藏样本
            samples.forEach(sample => {
                if (category === 'all' || sample.dataset.category === category) {
                    sample.style.display = 'block';
                } else {
                    sample.style.display = 'none';
                }
            });
        }
        
        function drawAccuracyChart() {
            // 这里可以集成Chart.js或其他图表库
            // 现在用简单的HTML/CSS创建图表
            const chartContainer = document.getElementById('accuracy-chart');
            if (!chartContainer) return;
            
            const data = {
                '基础模型': {mobile: 59.99, question: 49.49, scanned: 75.40},
                'Chain模型': {mobile: 68.10, question: 94.75, scanned: 78.63}
            };
            
            let chartHTML = '<div style="display: flex; gap: 20px; align-items: end; height: 250px; padding: 20px;">';
            
            const categories = ['mobile', 'question', 'scanned'];
            const categoryNames = {'mobile': '手机拍摄', 'question': '题目图片', 'scanned': '扫描文档'};
            const colors = {'基础模型': '#3498db', 'Chain模型': '#e74c3c'};
            
            categories.forEach(cat => {
                chartHTML += '<div style="flex: 1; text-align: center;">';
                chartHTML += `<div style="font-weight: bold; margin-bottom: 10px;">${categoryNames[cat]}</div>`;
                
                Object.keys(data).forEach((model, idx) => {
                    const value = data[model][cat];
                    const height = (value / 100) * 200;
                    chartHTML += `<div style="display: inline-block; width: 40px; height: ${height}px; background-color: ${colors[model]}; margin: 0 2px; vertical-align: bottom; border-radius: 3px 3px 0 0; position: relative;">`;
                    chartHTML += `<div style="position: absolute; top: -20px; left: 50%; transform: translateX(-50%); font-size: 10px; font-weight: bold;">${value.toFixed(1)}%</div>`;
                    chartHTML += '</div>';
                });
                
                chartHTML += '</div>';
            });
            
            chartHTML += '</div>';
            chartHTML += '<div style="display: flex; justify-content: center; gap: 20px; margin-top: 10px;">';
            Object.keys(colors).forEach(model => {
                chartHTML += `<div style="display: flex; align-items: center; gap: 5px;">`;
                chartHTML += `<div style="width: 15px; height: 15px; background-color: ${colors[model]}; border-radius: 2px;"></div>`;
                chartHTML += `<span style="font-size: 12px;">${model}</span>`;
                chartHTML += '</div>';
            });
            chartHTML += '</div>';
            
            chartContainer.innerHTML = chartHTML;
        }
        
        window.onload = function() {
            drawAccuracyChart();
        };
    """
    
    # 开始构建HTML
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR模型评测报告 - 详细版</title>
    <style>{css_styles}</style>
</head>
<body>
    <div class="container">
        <h1>OCR模型评测报告 - 详细版</h1>
        
        <div class="summary">
            <h2>评测概要</h2>
            <p><strong>评测时间：</strong>{data['evaluation_summary']['evaluation_timestamp']}</p>
            <p><strong>评测模型数量：</strong>{data['evaluation_summary']['total_models']}</p>
            <p><strong>测试类别：</strong>{', '.join(data['evaluation_summary']['categories'])}</p>
            <p><strong>评测说明：</strong>本次评测使用文本清理后的内容进行比较，准确率以标准答案字数为基准计算。</p>
        </div>
"""
    
    # 这里只是初始化，实际的HTML生成在后面
    return data, images_path

    # 添加可视化图表部分
    html_content += """
        <div class="charts-container">
            <div class="chart-box">
                <h3>各类别准确率对比</h3>
                <div id="accuracy-chart" class="chart-placeholder">准确率对比图表</div>
            </div>
        </div>

        <h2>统计数据表</h2>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>模型</th>
                    <th>类别</th>
                    <th>样本数</th>
                    <th>准确率</th>
                    <th>编辑距离</th>
                    <th>答案长度</th>
                    <th>预测长度</th>
                </tr>
            </thead>
            <tbody>
    """

    # 添加统计表格数据
    for model_result in data['model_results']:
        model_name = model_result['model_name']
        stats = model_result['statistics']

        # 总体行
        overall = stats['overall']
        accuracy = overall['avg_accuracy'] * 100
        accuracy_class = 'accuracy-high' if accuracy >= 80 else 'accuracy-medium' if accuracy >= 60 else 'accuracy-low'

        html_content += f"""
                <tr>
                    <td><strong>{model_name}</strong></td>
                    <td>总体</td>
                    <td>{overall['sample_count']}</td>
                    <td class="{accuracy_class}">{accuracy:.2f}%</td>
                    <td>{overall['avg_edit_distance']:.2f}</td>
                    <td>{overall['avg_gt_length']:.1f}</td>
                    <td>{overall['avg_pred_length']:.1f}</td>
                </tr>
        """

        # 各类别行
        categories = ['mobile', 'question', 'scanned']
        category_names = {'mobile': '手机拍摄', 'question': '题目图片', 'scanned': '扫描文档'}

        for category in categories:
            cat_stats = stats['by_category'][category]
            if cat_stats['sample_count'] > 0:
                accuracy = cat_stats['avg_accuracy'] * 100
                accuracy_class = 'accuracy-high' if accuracy >= 80 else 'accuracy-medium' if accuracy >= 60 else 'accuracy-low'

                html_content += f"""
                <tr>
                    <td>{model_name}</td>
                    <td>{category_names[category]}</td>
                    <td>{cat_stats['sample_count']}</td>
                    <td class="{accuracy_class}">{accuracy:.2f}%</td>
                    <td>{cat_stats['avg_edit_distance']:.2f}</td>
                    <td>{cat_stats['avg_gt_length']:.1f}</td>
                    <td>{cat_stats['avg_pred_length']:.1f}</td>
                </tr>
                """

    html_content += """
            </tbody>
        </table>

        <h2>详细样本对比</h2>
        <div class="category-filter">
            <button class="filter-btn active" onclick="filterSamples('all')">全部</button>
            <button class="filter-btn" onclick="filterSamples('mobile')">手机拍摄</button>
            <button class="filter-btn" onclick="filterSamples('question')">题目图片</button>
            <button class="filter-btn" onclick="filterSamples('scanned')">扫描文档</button>
        </div>

        <div class="sample-grid">
    """

    # 生成样本对比卡片
    # 首先收集所有样本数据
    samples_data = {}

    # 从基础模型结果中收集数据
    base_model_results = None
    chain_model_results = None

    for model_result in data['model_results']:
        if model_result['model_name'] == '基础模型':
            base_model_results = model_result['detailed_results']
        elif model_result['model_name'] == 'Chain模型':
            chain_model_results = model_result['detailed_results']

    # 创建样本映射
    if base_model_results:
        for result in base_model_results:
            filename = result['filename']
            samples_data[filename] = {
                'category': result['category'],
                'ground_truth': result['cleaned_gt_text'],
                'base_model': {
                    'text': result['cleaned_pred_text'],
                    'accuracy': result['accuracy'],
                    'edit_distance': result['edit_distance']
                }
            }

    if chain_model_results:
        for result in chain_model_results:
            filename = result['filename']
            if filename in samples_data:
                samples_data[filename]['chain_model'] = {
                    'text': result['cleaned_pred_text'],
                    'accuracy': result['accuracy'],
                    'edit_distance': result['edit_distance']
                }

    # 生成样本卡片
    for filename, sample_data in samples_data.items():
        if 'chain_model' not in sample_data:
            continue  # 跳过没有Chain模型结果的样本

        category = sample_data['category']
        category_names = {'mobile': '手机拍摄', 'question': '题目图片', 'scanned': '扫描文档'}

        # 查找对应的图片文件
        image_found = False
        image_data_uri = None

        # 尝试不同的图片扩展名和路径
        possible_extensions = ['.jpg', '.jpeg', '.png']
        possible_paths = [
            images_path / category / f"{filename}.jpg",
            images_path / category / f"{filename}.jpeg",
            images_path / category / f"{filename}.png",
            images_path / category / f"{filename}-1.jpg",
            images_path / category / f"{filename}-1.jpeg",
            images_path / category / f"{filename}-1.png"
        ]

        for img_path in possible_paths:
            if img_path.exists():
                image_data_uri = encode_image_to_base64(img_path)
                if image_data_uri:
                    image_found = True
                    break

        # 生成样本卡片HTML
        html_content += f"""
            <div class="sample-card" data-category="{category}">
                <div class="sample-header">
                    {filename} - {category_names.get(category, category)}
                </div>
        """

        if image_found and image_data_uri:
            html_content += f"""
                <div class="image-container">
                    <img src="{image_data_uri}" alt="{filename}" class="sample-image">
                </div>
            """
        else:
            html_content += f"""
                <div class="image-container">
                    <div style="padding: 40px; background-color: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; color: #6c757d;">
                        图片未找到: {filename}
                    </div>
                </div>
            """

        # 文本对比部分
        base_accuracy = sample_data['base_model']['accuracy'] * 100
        chain_accuracy = sample_data['chain_model']['accuracy'] * 100

        base_class = 'accuracy-high' if base_accuracy >= 80 else 'accuracy-medium' if base_accuracy >= 60 else 'accuracy-low'
        chain_class = 'accuracy-high' if chain_accuracy >= 80 else 'accuracy-medium' if chain_accuracy >= 60 else 'accuracy-low'

        html_content += f"""
                <div class="text-comparison">
                    <div class="text-box ground-truth">
                        <h4>标准答案</h4>
                        <div class="text-content">{sample_data['ground_truth']}</div>
                    </div>
                    <div class="text-box base-model">
                        <h4>基础模型</h4>
                        <div class="text-content">{sample_data['base_model']['text']}</div>
                        <div class="metrics">
                            <span class="metric {base_class}">准确率: {base_accuracy:.1f}%</span>
                            <span class="metric">编辑距离: {sample_data['base_model']['edit_distance']}</span>
                        </div>
                    </div>
                    <div class="text-box chain-model">
                        <h4>Chain模型</h4>
                        <div class="text-content">{sample_data['chain_model']['text']}</div>
                        <div class="metrics">
                            <span class="metric {chain_class}">准确率: {chain_accuracy:.1f}%</span>
                            <span class="metric">编辑距离: {sample_data['chain_model']['edit_distance']}</span>
                        </div>
                    </div>
                </div>
            </div>
        """

    # 完成HTML
    html_content += f"""
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #6c757d;">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>评测系统版本: v2.0 Enhanced</p>
        </div>
    </div>

    <script>
        {js_code}
    </script>
</body>
</html>
    """

    return html_content

def main():
    print("开始生成增强版HTML报告...")

    # 读取JSON数据
    with open("evaluation_results.json", 'r', encoding='utf-8') as f:
        data = json.load(f)

    test_dataset = Path("test_dataset")
    images_path = test_dataset / "images"

    # CSS样式
    css_styles = """
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 30px;
        }
        .summary {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .charts-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }
        .chart-box {
            flex: 1;
            min-width: 300px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
        }
        .sample-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(800px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .sample-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .sample-header {
            background-color: #34495e;
            color: white;
            padding: 10px 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 10px 10px 0 0;
            font-weight: bold;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .sample-image {
            max-width: 100%;
            max-height: 400px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .text-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        .text-box {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .text-box h4 {
            margin: 0 0 10px 0;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 14px;
        }
        .ground-truth h4 {
            background-color: #d4edda;
            color: #155724;
        }
        .base-model h4 {
            background-color: #fff3cd;
            color: #856404;
        }
        .chain-model h4 {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .text-content {
            font-size: 13px;
            line-height: 1.5;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .metrics {
            display: flex;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        .metric {
            background-color: #e9ecef;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: bold;
        }
        .accuracy-high { background-color: #d4edda; color: #155724; }
        .accuracy-medium { background-color: #fff3cd; color: #856404; }
        .accuracy-low { background-color: #f8d7da; color: #721c24; }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        .stats-table th {
            background-color: #34495e;
            color: white;
            font-weight: bold;
        }
        .stats-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .category-filter {
            margin: 20px 0;
            text-align: center;
        }
        .filter-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .filter-btn:hover {
            background-color: #2980b9;
        }
        .filter-btn.active {
            background-color: #e74c3c;
        }

        .chart-placeholder {
            width: 100%;
            height: 300px;
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 16px;
            border-radius: 8px;
        }
    """

    # JavaScript代码
    js_code = """
        function filterSamples(category) {
            const samples = document.querySelectorAll('.sample-card');
            const buttons = document.querySelectorAll('.filter-btn');

            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 显示/隐藏样本
            samples.forEach(sample => {
                if (category === 'all' || sample.dataset.category === category) {
                    sample.style.display = 'block';
                } else {
                    sample.style.display = 'none';
                }
            });
        }

        function drawAccuracyChart() {
            // 这里可以集成Chart.js或其他图表库
            // 现在用简单的HTML/CSS创建图表
            const chartContainer = document.getElementById('accuracy-chart');
            if (!chartContainer) return;

            const data = {
                '基础模型': {mobile: 59.99, question: 49.49, scanned: 75.40},
                'Chain模型': {mobile: 68.10, question: 94.75, scanned: 78.63}
            };

            let chartHTML = '<div style="display: flex; gap: 20px; align-items: end; height: 250px; padding: 20px;">';

            const categories = ['mobile', 'question', 'scanned'];
            const categoryNames = {'mobile': '手机拍摄', 'question': '题目图片', 'scanned': '扫描文档'};
            const colors = {'基础模型': '#3498db', 'Chain模型': '#e74c3c'};

            categories.forEach(cat => {
                chartHTML += '<div style="flex: 1; text-align: center;">';
                chartHTML += `<div style="font-weight: bold; margin-bottom: 10px;">${categoryNames[cat]}</div>`;

                Object.keys(data).forEach((model, idx) => {
                    const value = data[model][cat];
                    const height = (value / 100) * 200;
                    chartHTML += `<div style="display: inline-block; width: 40px; height: ${height}px; background-color: ${colors[model]}; margin: 0 2px; vertical-align: bottom; border-radius: 3px 3px 0 0; position: relative;">`;
                    chartHTML += `<div style="position: absolute; top: -20px; left: 50%; transform: translateX(-50%); font-size: 10px; font-weight: bold;">${value.toFixed(1)}%</div>`;
                    chartHTML += '</div>';
                });

                chartHTML += '</div>';
            });

            chartHTML += '</div>';
            chartHTML += '<div style="display: flex; justify-content: center; gap: 20px; margin-top: 10px;">';
            Object.keys(colors).forEach(model => {
                chartHTML += `<div style="display: flex; align-items: center; gap: 5px;">`;
                chartHTML += `<div style="width: 15px; height: 15px; background-color: ${colors[model]}; border-radius: 2px;"></div>`;
                chartHTML += `<span style="font-size: 12px;">${model}</span>`;
                chartHTML += '</div>';
            });
            chartHTML += '</div>';

            chartContainer.innerHTML = chartHTML;
        }

        window.onload = function() {
            drawAccuracyChart();
        };
    """

    # 开始构建HTML
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR模型评测报告 - 详细版</title>
    <style>{css_styles}</style>
</head>
<body>
    <div class="container">
        <h1>OCR模型评测报告 - 详细版</h1>

        <div class="summary">
            <h2>评测概要</h2>
            <p><strong>评测时间：</strong>{data['evaluation_summary']['evaluation_timestamp']}</p>
            <p><strong>评测模型数量：</strong>{data['evaluation_summary']['total_models']}</p>
            <p><strong>测试类别：</strong>{', '.join(data['evaluation_summary']['categories'])}</p>
            <p><strong>评测说明：</strong>本次评测使用文本清理后的内容进行比较，准确率以标准答案字数为基准计算。</p>
        </div>

        <div class="charts-container">
            <div class="chart-box">
                <h3>各类别准确率对比</h3>
                <div id="accuracy-chart" class="chart-placeholder">准确率对比图表</div>
            </div>
        </div>

        <h2>统计数据表</h2>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>模型</th>
                    <th>类别</th>
                    <th>样本数</th>
                    <th>准确率</th>
                    <th>编辑距离</th>
                    <th>答案长度</th>
                    <th>预测长度</th>
                </tr>
            </thead>
            <tbody>
    """

    # 添加统计表格数据
    for model_result in data['model_results']:
        model_name = model_result['model_name']
        stats = model_result['statistics']

        # 总体行
        overall = stats['overall']
        accuracy = overall['avg_accuracy'] * 100
        accuracy_class = 'accuracy-high' if accuracy >= 80 else 'accuracy-medium' if accuracy >= 60 else 'accuracy-low'

        html_content += f"""
                <tr>
                    <td><strong>{model_name}</strong></td>
                    <td>总体</td>
                    <td>{overall['sample_count']}</td>
                    <td class="{accuracy_class}">{accuracy:.2f}%</td>
                    <td>{overall['avg_edit_distance']:.2f}</td>
                    <td>{overall['avg_gt_length']:.1f}</td>
                    <td>{overall['avg_pred_length']:.1f}</td>
                </tr>
        """

        # 各类别行
        categories = ['mobile', 'question', 'scanned']
        category_names = {'mobile': '手机拍摄', 'question': '题目图片', 'scanned': '扫描文档'}

        for category in categories:
            cat_stats = stats['by_category'][category]
            if cat_stats['sample_count'] > 0:
                accuracy = cat_stats['avg_accuracy'] * 100
                accuracy_class = 'accuracy-high' if accuracy >= 80 else 'accuracy-medium' if accuracy >= 60 else 'accuracy-low'

                html_content += f"""
                <tr>
                    <td>{model_name}</td>
                    <td>{category_names[category]}</td>
                    <td>{cat_stats['sample_count']}</td>
                    <td class="{accuracy_class}">{accuracy:.2f}%</td>
                    <td>{cat_stats['avg_edit_distance']:.2f}</td>
                    <td>{cat_stats['avg_gt_length']:.1f}</td>
                    <td>{cat_stats['avg_pred_length']:.1f}</td>
                </tr>
                """

    html_content += """
            </tbody>
        </table>

        <h2>详细样本对比</h2>
        <div class="category-filter">
            <button class="filter-btn active" onclick="filterSamples('all')">全部</button>
            <button class="filter-btn" onclick="filterSamples('mobile')">手机拍摄</button>
            <button class="filter-btn" onclick="filterSamples('question')">题目图片</button>
            <button class="filter-btn" onclick="filterSamples('scanned')">扫描文档</button>
        </div>

        <div class="sample-grid">
    """

    # 生成样本对比卡片
    # 首先收集所有样本数据
    samples_data = {}

    # 从基础模型结果中收集数据
    base_model_results = None
    chain_model_results = None

    for model_result in data['model_results']:
        if model_result['model_name'] == '基础模型':
            base_model_results = model_result['detailed_results']
        elif model_result['model_name'] == 'Chain模型':
            chain_model_results = model_result['detailed_results']

    # 创建样本映射
    if base_model_results:
        for result in base_model_results:
            filename = result['filename']
            samples_data[filename] = {
                'category': result['category'],
                'ground_truth': result['cleaned_gt_text'],
                'base_model': {
                    'text': result['cleaned_pred_text'],
                    'accuracy': result['accuracy'],
                    'edit_distance': result['edit_distance']
                }
            }

    if chain_model_results:
        for result in chain_model_results:
            filename = result['filename']
            if filename in samples_data:
                samples_data[filename]['chain_model'] = {
                    'text': result['cleaned_pred_text'],
                    'accuracy': result['accuracy'],
                    'edit_distance': result['edit_distance']
                }

    # 生成样本卡片
    for filename, sample_data in samples_data.items():
        if 'chain_model' not in sample_data:
            continue  # 跳过没有Chain模型结果的样本

        category = sample_data['category']
        category_names = {'mobile': '手机拍摄', 'question': '题目图片', 'scanned': '扫描文档'}

        # 查找对应的图片文件
        image_found = False
        image_data_uri = None

        # 尝试不同的图片扩展名和路径
        possible_extensions = ['.jpg', '.jpeg', '.png']
        possible_paths = [
            images_path / category / f"{filename}.jpg",
            images_path / category / f"{filename}.jpeg",
            images_path / category / f"{filename}.png",
            images_path / category / f"{filename}-1.jpg",
            images_path / category / f"{filename}-1.jpeg",
            images_path / category / f"{filename}-1.png"
        ]

        for img_path in possible_paths:
            if img_path.exists():
                image_data_uri = encode_image_to_base64(img_path)
                if image_data_uri:
                    image_found = True
                    break

        # 生成样本卡片HTML
        html_content += f"""
            <div class="sample-card" data-category="{category}">
                <div class="sample-header">
                    {filename} - {category_names.get(category, category)}
                </div>
        """

        if image_found and image_data_uri:
            html_content += f"""
                <div class="image-container">
                    <img src="{image_data_uri}" alt="{filename}" class="sample-image">
                </div>
            """
        else:
            html_content += f"""
                <div class="image-container">
                    <div style="padding: 40px; background-color: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; color: #6c757d;">
                        图片未找到: {filename}
                    </div>
                </div>
            """

        # 文本对比部分
        base_accuracy = sample_data['base_model']['accuracy'] * 100
        chain_accuracy = sample_data['chain_model']['accuracy'] * 100

        base_class = 'accuracy-high' if base_accuracy >= 80 else 'accuracy-medium' if base_accuracy >= 60 else 'accuracy-low'
        chain_class = 'accuracy-high' if chain_accuracy >= 80 else 'accuracy-medium' if chain_accuracy >= 60 else 'accuracy-low'

        html_content += f"""
                <div class="text-comparison">
                    <div class="text-box ground-truth">
                        <h4>标准答案</h4>
                        <div class="text-content">{sample_data['ground_truth']}</div>
                    </div>
                    <div class="text-box base-model">
                        <h4>基础模型</h4>
                        <div class="text-content">{sample_data['base_model']['text']}</div>
                        <div class="metrics">
                            <span class="metric {base_class}">准确率: {base_accuracy:.1f}%</span>
                            <span class="metric">编辑距离: {sample_data['base_model']['edit_distance']}</span>
                        </div>
                    </div>
                    <div class="text-box chain-model">
                        <h4>Chain模型</h4>
                        <div class="text-content">{sample_data['chain_model']['text']}</div>
                        <div class="metrics">
                            <span class="metric {chain_class}">准确率: {chain_accuracy:.1f}%</span>
                            <span class="metric">编辑距离: {sample_data['chain_model']['edit_distance']}</span>
                        </div>
                    </div>
                </div>
            </div>
        """

    # 完成HTML
    html_content += f"""
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #6c757d;">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>评测系统版本: v2.0 Enhanced</p>
        </div>
    </div>

    <script>
        {js_code}
    </script>
</body>
</html>
    """

    # 保存HTML文件
    with open("enhanced_evaluation_report.html", 'w', encoding='utf-8') as f:
        f.write(html_content)

    print("增强版HTML报告已生成: enhanced_evaluation_report.html")

if __name__ == "__main__":
    main()
